<script setup lang="ts">
import { onMounted, ref, computed, nextTick } from "vue";
import WeightsCalcu from "./WeightsCalcu.vue";
import DynamicFilter from "./common/DynamicFilter.vue";
import axios from "axios";
import { baseURL, baseFastapiURL } from "../main";
import { Loader, Download } from "lucide-vue-next";
import {
  setOptimizerRunStatus,
  getOptimizerRunStatus,
  dynamicFilters,
} from "../services/api.js";

const tableData = ref([]);
const currentPage = ref(1);
const rowsPerPage = 20;
const totalRows = ref(0);
const weightsConfigure = ref(false);
const isLoading = ref(false);
const selectedStores = ref("");
const selectedSubClasses = ref([]);
const actionOptions = ["no change", "drop", "increase", "decrease"];
const isTableLoading = ref(false);
const loadingMessage = ref("");
const scrollContainer = ref(null);
const loading = ref(false);
let isFetchingNextPage = ref(false);
const filterMeta = ref([]);
const isDownload = ref(false);
const scenarioId = sessionStorage.getItem("scenario_id");
const territory = sessionStorage.getItem("territory_name");
const concept = sessionStorage.getItem("concept");
let lastScrollTop = 0;

const categoryMap = {
  L: "Low",
  M: "Medium",
  H: "High",
};

async function fetchFilterMeta() {
  const data = await dynamicFilters(
    scenarioId,
    concept,
    territory,
    "performance"
  );
  if (data.status === "success") {
    filterMeta.value = data.filters;
    if (data.filters.length > 0) {
      selectedStores.value = data.filters[0].LOC_CD;
    }
  }
}
const props = defineProps({
  scenarioPhase: {
    type: String,
    default: ''
  }
})
const isDisabled = computed(() => props.scenarioPhase === 'completed')
onMounted(() => {
  fetchFilterMeta();
});

const mapCategory = (value: string): string => {
  return categoryMap[value] || value;
};

async function updateWeights(performanceWeights) {
  try {
    const response = await axios.post("/scenario/update_weights/", {
      scenario_id: sessionStorage.getItem("scenario_id"),
      weights: performanceWeights,
    });
  } catch (err) {
    console.error("Error fetching data:", err);
  }
}

const showFiltered = ref(false);
const selectedRecommendation = ref([]);
const uniqueRecommendations = computed(() => {
  const recommendations = ["Increase", "Decrease", "Keep"];
  return recommendations.map((rec) => ({ value: rec, label: rec }));
});

// const selectedGrade = ref([])
// const uniqueGrades = computed(() => {
//   const grades = ['MMH', 'MML','HLM','HLL','LML','LLL','MMM','HHL','LHH','LLM','MLL','MHH','LHM','HMM']
//   return grades.map(grade => ({ value: grade, label: grade }))
// })

const paginatedData = computed(() => {
  return tableData.value.slice(0, currentPage.value * rowsPerPage);
});

async function callPreFetchApi(module_name) {
  try {
    const scenario_id = sessionStorage.getItem("scenario_id");
    const response = await axios.post(
      `${baseFastapiURL}/run/${module_name}`,
      {
        SCENARIO_ID: scenario_id,
      },
      {
        headers: { "Content-Type": "application/json" },
      }
    );
    return response.data;
  } catch (e) {
    console.error("Failed to call pre-fetch API", e);
    throw e;
  }
}

const runPerformanceApi = async () => {
  isTableLoading.value = true;
  try {
    loadingMessage.value = "Analysing your data...";
    await callPreFetchApi("datapreparation");
    loadingMessage.value = "Measuring saturation levels...";
    await callPreFetchApi("saturation");
    loadingMessage.value = "Setting things up...";
    await new Promise((resolve) => setTimeout(resolve, 5000));
    loadingMessage.value = "Fetching data...";
    await fetchFilterMeta();
    await fetchPerformanceData(currentPage, rowsPerPage);
    await checkNeedForPagination();

    await setOptimizerRunStatus({
      scenario_id: sessionStorage.getItem("scenario_id"),
      run_optimizer: 0,
      run_performance: 1,
      run_range: 0
    });
  } catch (e) {
    console.error("Failed to fetch performance data", e);
  } finally {
    isTableLoading.value = false;
  }
};
async function fetchPerformanceData(page = 1, pageSize = 20, isAppend = false) {
  try {
    const scenario_id = sessionStorage.getItem("scenario_id");
    const response = await axios.post(
      `${baseURL}/scenario/fetch_filtered_performance_data/`,
      {
        scenario_id: scenario_id,
        concept: sessionStorage.getItem("concept"),
        territory: sessionStorage.getItem("territory_name"),
        store_ids: Array.isArray(selectedStores.value)
          ? selectedStores.value
          : [selectedStores.value],
        page: page.value,
        page_size: pageSize,
        recommendation: selectedRecommendation.value,
        subclass_names: selectedSubClasses.value,

      },
      {
        headers: { "Content-Type": "application/json" },
      }
    );
    if (isAppend) {
      tableData.value = [...tableData.value, ...response.data?.data];
    } else {
      tableData.value = response.data?.data;
    }
    totalRows.value = response.data?.total_count;
    if (response.data.length === 0) {
      console.log("No performance data found, re-running calculation...");
      runPerformanceApi();
    }
  } catch (e) {
    console.error("Failed to fetch performance data", e);
  }
}

onMounted(async () => {
  // First: load filters and set selectedStores
  await fetchFilterMeta();

  // Then: check optimizer run status and fetch data
  const res = await getOptimizerRunStatus(
    sessionStorage.getItem("scenario_id"),
    "runPerformance"
  );

  if (!res) {
    await runPerformanceApi();
  } else {
    await fetchPerformanceData(currentPage, rowsPerPage);
    await checkNeedForPagination();
  }
});

window.addEventListener("resize", () => {
  checkNeedForPagination();
});
const getActionClass = (action) => {
  if (!action) return "bg-gray-100 text-gray-600";

  const normalized = action.toLowerCase();
  if (normalized.includes("increase")) {
    return "bg-green-300 text-green-800 ";
  } else if (normalized.includes("decrease")) {
    return "bg-red-100 text-red-700";
  }
  return "bg-gray-100 text-gray-600";
};

const toggleAction = async (row, value) => {
  row.userAction = value;
  // Prepare payload
  const payload = {
    group_name: row.GRP_NM,
    department_name: row.DPT_NM,
    class_name: row.CLSS_NM,
    subclass_name: row.SUB_CLSS_NM,
    location_code: row.LOC_CD,
    new_action: value,
    scenario_id: sessionStorage.getItem("scenario_id"),
    concept: sessionStorage.getItem("concept"),
  };

  try {
    const response = await axios.post(
      `${baseURL}/scenario/update_performance_action/`,
      payload
    );
    await setOptimizerRunStatus({
      scenario_id: sessionStorage.getItem("scenario_id"),
      run_optimizer: 0,
      run_performance: 1,
      run_range: 0
    });
  } catch (error) {
    console.error(
      "Error updating action:",
      error.response?.data || error.message
    );
  }
};

function goToPage(page: number) {
  if (page < 1 || page > Math.ceil(totalRows.value / rowsPerPage)) return;
  currentPage.value = page;
}

function handleApplyWeights(weights) {
  updateWeights(weights);
  sessionStorage.setItem("weights", JSON.stringify(weights));
  weightsConfigure.value = false;
  runPerformanceApi();
}
const checkNeedForPagination = async () => {
  const el = scrollContainer.value;
  if (!el || isLoading.value || isFetchingNextPage.value) return;
  const isScrollable = el.scrollHeight > el.clientHeight;
  // If not scrollable AND there are more pages to fetch
  if (!isScrollable && currentPage.value < totalPages.value) {
    isFetchingNextPage.value = true;
    currentPage.value += 1;

    await fetchPerformanceData(currentPage, rowsPerPage, true);
    isFetchingNextPage.value = false;

    // Wait for DOM update then check again
    await nextTick();
    checkNeedForPagination();
  }
};

const uniqueStores = computed(() => {
  const stores = filterMeta.value.map((item) => ({
    value: item.LOC_CD,
    label: `${item.LOC_CD} - ${item.LOC_NM}`,
  }));
  return [...new Map(stores.map((item) => [item.value, item])).values()];
});
const uniqueSubClasses = computed(() => {
  const stores = filterMeta.value.map((item) => ({
    value: item.SUB_CLSS_NM,
    label: item.SUB_CLSS_NM,
  }));
  return [...new Map(stores.map((item) => [item.value, item])).values()];
});
function clearAllFilters() {
  selectedStores.value = "";
  selectedSubClasses.value = [];
}
const filteredData = computed(() => {
  return tableData.value.filter((row) => {
    const matchStore =
      selectedStores.value.length === 0 ||
      selectedStores.value.includes(row.LOC_CD);
    const matchSubClass =
      selectedSubClasses.value.length === 0 ||
      selectedSubClasses.value.includes(row.SUB_CLSS_NM);
    return matchStore && matchSubClass;
  });
});
async function handleFilter() {
  try {
    loadingMessage.value = "Applying filters...";
    isTableLoading.value = true;
    const scenario_id = sessionStorage.getItem("scenario_id");
    const response = await axios.post(
      `${baseURL}/scenario/fetch_filtered_performance_data/`,
      {
        scenario_id: scenario_id,
        concept: sessionStorage.getItem("concept"),
        territory: sessionStorage.getItem("territory_name"),
        store_ids: Array.isArray(selectedStores.value)
          ? selectedStores.value
          : [selectedStores.value],
        subclass_names: selectedSubClasses.value,
        recommendation: selectedRecommendation.value,
        page: 1,
        page_size: rowsPerPage,
      }
    );

    tableData.value = response.data?.data || [];
    totalRows.value = response.data?.total_count || 0;
    currentPage.value = 1;
    showFiltered.value = true;
    await checkNeedForPagination();
  } catch (error) {
    console.error("Error applying filters:", error);
  } finally {
    isTableLoading.value = false;
  }
}

const capitalizeWords = (str: string): string => {
  return str.toLowerCase().replace(/\b\w/g, (char) => char.toUpperCase());
};

function debounce(fn, delay = 200) {
  let timeout;
  return function (...args) {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => fn.apply(this, args), delay);
  };
}

const totalPages = computed(() => Math.ceil(totalRows.value / rowsPerPage));

const handleScroll = async () => {
  const el = scrollContainer.value;
  if (!el || loading.value) return;

  // Only proceed if vertical scroll happened
  const currentScrollTop = el.scrollTop;
  if (currentScrollTop === lastScrollTop) return;

  lastScrollTop = currentScrollTop;

  const threshold = 100;
  const isNearBottom =
    el.scrollTop + el.clientHeight >= el.scrollHeight - threshold;
  if (isNearBottom && currentPage.value < totalPages.value) {
    isFetchingNextPage.value = true;
    currentPage.value++;
    await fetchPerformanceData(currentPage, rowsPerPage, true);
    isFetchingNextPage.value = false;
  }
};

const debouncedScrollHandler = debounce(handleScroll, 200);

const handelStoreChange = () => {
  selectedSubClasses.value = [];
};

const actionColor = (action, newAction, actionP) => {
  if (!action) return "bg-gray-100 text-gray-600";

  const normalized = action.toLowerCase();

  // If new_action and action_p are different → highlight
  if (newAction && actionP && newAction !== actionP) {
    if (normalized.includes("increase")) {
      return "bg-green-200 text-green-700";
    } else if (normalized.includes("decrease")) {
      return "bg-red-200 text-red-900";
    } else {
      return "bg-yellow-100 text-yellow-800";
    }
  }

  return "bg-gray-100 text-gray-600";
};

async function downloadPerformanceCSV() {
  isDownload.value = true;
  try {
    const scenario_id = sessionStorage.getItem("scenario_id");
    const concept = sessionStorage.getItem("concept");
    const territory = sessionStorage.getItem("territory_name");

    if (!scenario_id || !concept || !territory) {
      console.error("Missing required parameters for download");
      return;
    }

    // Prepare the same payload as the filter function to get all filtered data
    const payload = {
      scenario_id: scenario_id,
      concept: concept,
      territory: territory,
    };

    const response = await axios.post(
      `${baseURL}/scenario/downloadPerformanceData/`,
      payload,
      {
        headers: { "Content-Type": "application/json" },
      }
    );

    const data = response.data?.data;

    if (!data || data.length === 0) {
      alert("No data available for download.");
      return;
    }

    // Define CSV columns based on the table structure
    const csvColumns = [
      { key: "LOC_CD", label: "Store" },
      { key: "SUB_CLSS_NM", label: "Subclass" },
      { key: "Linear_meter", label: "Linear meter" },
      { key: "d_units", label: "Units per Invoice" },
      { key: "CUST_PEN", label: "Customer Penetration %" },
      { key: "COVER", label: "Cover" },
      { key: "MARGIN_PERC", label: "Margin" },
      { key: "Productivity", label: "Productivity" },
      { key: "ASP", label: "Average Selling Price" },
      { key: "Performance", label: "Performance" },
      { key: "perf_bucket_p", label: "Performance Grade" },
      { key: "lm_bucket_p", label: "LM Grade" },
      { key: "cover_bucket_p", label: "Cover Grade" },
      { key: "final_bucket_p", label: "Final Grade" },
      { key: "action_p", label: "Recommended Action" },
      { key: "new_action", label: "Chosen Action" },
    ];

    // Create CSV header
    const header = csvColumns.map((col) => col.label).join(",");

    // Create CSV rows
    const rows = data.map((row) => {
      return csvColumns
        .map((col) => {
          let val = row[col.key];

          // Format specific columns
          if (col.key === "SUB_CLSS_NM") {
            val = capitalizeWords(val || "");
          } else if (
            col.key === "Linear_meter" ||
            col.key === "d_units" ||
            col.key === "Productivity" ||
            col.key === "ASP"
          ) {
            val = val ? val.toFixed(2) : "0.00";
          } else if (col.key === "CUST_PEN" || col.key === "MARGIN_PERC") {
            val = val ? val.toFixed(2) + "%" : "0.00%";
          } else if (col.key === "COVER") {
            val = val ? Math.round(val) : "0";
          } else if (
            col.key === "perf_bucket_p" ||
            col.key === "lm_bucket_p" ||
            col.key === "cover_bucket_p"
          ) {
            val = mapCategory(val || "");
          }

          // Escape CSV values
          if (
            typeof val === "string" &&
            (val.includes(",") || val.includes('"') || val.includes("\n"))
          ) {
            val = '"' + val.replace(/"/g, '""') + '"';
          }

          return val ?? "";
        })
        .join(",");
    });

    // Create and download CSV
    const csvContent = [header, ...rows].join("\r\n");
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `performance_calculation_${new Date()
      .toISOString()
      .slice(0, 10)}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error("Error downloading CSV:", error);
    alert("Failed to download CSV. Please try again.");
  } finally {
    isDownload.value = false;
  }
}
</script>

<template>
  <div class="flex w-full">
    <div
      v-if="isTableLoading"
      class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50"
    >
      <div class="bg-white rounded-lg p-2 flex items-center justify-center">
        <div class="tw-loader"></div> 
        <div class="mr-3">{{ loadingMessage }}</div>
      </div>
    </div>
    <div class="flex-1 flex flex-col overflow-hidden">
      <div class="">
        <div class="px-8">
          <div class="bg-white rounded-lg shadow-sm p-2 mb-2">
            <div
              class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4 items-end"
            >
              <div>
                <label class="ml-2 text-sm font-medium text-gray-700">
                  Store <span class="text-red-500 ml-1">*</span>
                </label>
                <DynamicFilter
                  v-model="selectedStores"
                  :multiselect="false"
                  :close-on-select="!multiselect"
                  label="Store"
                  placeholder="Stores"
                  :options="uniqueStores"
                  :searchable="(uniqueStores?.length || 0) > 10"
                  variant="secondary"
                  size="sm"
                  @change="handelStoreChange()"
                />
              </div>
              <div>
                <label class="ml-2 text-sm font-medium text-gray-700"
                  >Sub Class</label
                >
                <DynamicFilter
                  v-model="selectedSubClasses"
                  :multiselect="true"
                  label="Sub Class"
                  placeholder="Sub Class"
                  :options="uniqueSubClasses"
                  :searchable="(uniqueSubClasses?.length || 0) > 10"
                  variant="secondary"
                  size="sm"
                />
              </div>
              <div>
                <label class="ml-2 text-sm font-medium text-gray-700"
                  >Recommendation</label
                >
                <DynamicFilter
                  v-model="selectedRecommendation"
                  :multiselect="true"
                  label="Recommendation"
                  placeholder="Recommendation"
                  :options="uniqueRecommendations"
                  :searchable="(uniqueRecommendations?.length || 0) > 10"
                  variant="secondary"
                  size="sm"
                  class="w-full"
                />
              </div>
              <div class="flex items-end gap-2">
                <button
                  @click="clearAllFilters()"
                  class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-secondary"
                >
                  Clear All
                </button>
                <button
                  @click="handleFilter()"
                  class="rounded-lg px-4 py-2 text-sm font-medium border bg-tertiary hover:bg-tertiary text-tx-primary focus:ring-secondary"
                >
                  Apply Filters
                </button>
              </div>
              <div
                class="flex justify-end items-start gap-2 col-span-1 lg:col-span-4"
              >
                <button
                  class="font-medium text-sm px-4 py-2 bg-tertiary hover:bg-secondary text-tx-primary rounded-lg shadow transition"
                  @click="weightsConfigure = true"
                >
                  Configure Weights
                </button>
                <button
                  @click="downloadPerformanceCSV()"
                  class="flex items-center gap-2 px-4 py-2 mr-4 bg-tertiary hover:bg-secondary text-tx-primary text-sm font-medium rounded-lg shadow transition"
                  :disabled="isDownload"
                >
                  <div
                    v-if="isDownload"
                    class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"
                  ></div>
                  <Download v-else class="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <main class="flex-1 px-4 pb-4 sm:px-6 sm:pb-6 lg:px-8 lg:pb-8">
        <div
          v-if="isLoading"
          class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50"
        >
            <div class="bg-white rounded-lg p-2 flex items-center justify-center">
              <div class="tw-loader"></div> 
              <div class="mr-3">Updating chart...</div>
            </div>
        </div>
        <!-- <div v-if="isTableLoading" class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
          <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-secondary"></div>
            <span class="text-gray-700">{{ loadingMessage }}</span>
          </div>
        </div> -->
        <!-- <div class="justify-end flex space-x-4 mb-4"> -->
        <!-- <div>
            <button class="px-4 py-2 bg-secondary hover:bg-secondary text-tx-primary rounded-lg shadow transition"
              @click="weightsConfigure = true">
              Configure Weights
            </button>
          </div> -->
        <!-- <div>
            <button
              class="px-4 py-1 bg-secondary hover:bg-secondary text-tx-primary text-lg font-semibold rounded-lg shadow transition"
              @click="updateWeights">
              Recalculate
            </button>
          </div> -->
        <!-- </div> -->
        <div class="mt-2 rounded-lg shadow">
          <div class="overflow-x-auto rounded">
            <div
              class="max-h-[70vh] overflow-y-auto relative"
              @scroll="debouncedScrollHandler"
              ref="scrollContainer"
            >
              <table
                class="w-full text-xs text-left border border-separate border-spacing-0 border-gray-300 bg-white"
                style="--store-col-width: 60px"
              >
                <thead class="bg-primary">
                  <tr>
                    <th
                      class="px-2 py-1 border sticky top-0 left-0 z-40 bg-primary min-w-[60px] text-center"
                    >
                      Store
                    </th>
                    <th
                      class="px-2 py-1 border sticky top-0 z-40 bg-primary min-w-[100px] text-center"
                      style="left: var(--store-col-width)"
                    >
                      Subclass
                    </th>
                    <th
                      class="px-2 py-1 border sticky top-0 z-30 bg-primary whitespace-nowrap"
                    >
                      Linear meter
                    </th>
                    <th
                      class="px-2 py-1 border sticky top-0 z-30 bg-primary whitespace-nowrap"
                    >
                      Units per Invoice
                    </th>
                    <th
                      class="px-2 py-1 border sticky top-0 z-30 bg-primary whitespace-nowrap"
                    >
                      Customer Penetration
                    </th>
                    <th class="px-2 py-1 border sticky top-0 z-30 bg-primary">
                      Cover
                    </th>
                    <th
                      class="px-2 py-1 border sticky top-0 z-30 bg-primary whitespace-nowrap"
                    >
                      Margin
                    </th>
                    <th class="px-2 py-1 border sticky top-0 z-30 bg-primary">
                      Productivity
                    </th>
                    <th
                      class="px-2 py-1 border sticky top-0 z-30 bg-primary text-nowrap"
                    >
                      Average Selling Price
                    </th>
                    <th
                      class="px-2 py-1 border sticky top-0 z-30 bg-primary whitespace-nowrap overflow-visible relative"
                    >
                      <div class="inline-flex items-center group relative">
                        <span>Performance</span>
                        <span
                          class="ml-1 text-gray-400 hover:text-gray-700 font-bold border border-gray-300 rounded-full w-4 h-4 flex items-center justify-center text-[0.55rem] bg-white cursor-pointer"
                          aria-label="Information"
                        >
                          i
                        </span>

                        <!-- Tooltip -->
                        <div
                          class="absolute top-full left-1/2 -translate-x-1/2 mt-1 w-64 rounded-md bg-gray-800 text-white text-[11px] leading-snug p-2.5 shadow-lg z-50 opacity-0 pointer-events-none transition-opacity group-hover:opacity-100 group-hover:pointer-events-auto whitespace-normal break-words"
                        >
                          <div>
                            A normalized index that combines ASP, Margin,
                            Customer Penetration, Units per Invoice, and
                            Productivity, with user-defined weights, to
                            holistically measure category performance for space
                            optimization.
                          </div>
                        </div>
                      </div>
                    </th>

                    <th
                      class="px-2 py-1 border sticky top-0 z-30 bg-primary whitespace-nowrap"
                    >
                      Performance Grade
                    </th>
                    <th
                      class="px-2 py-1 border sticky top-0 z-30 bg-primary whitespace-nowrap"
                    >
                      LM Grade
                    </th>
                    <th
                      class="px-2 py-1 border sticky top-0 z-30 bg-primary whitespace-nowrap"
                    >
                      Cover Grade
                    </th>
                    <th
                      class="px-2 py-1 border sticky top-0 z-30 bg-primary whitespace-nowrap"
                    >
                      Final Grade
                    </th>
                    <th
                      class="px-2 py-1 border sticky top-0 z-30 bg-primary whitespace-nowrap"
                    >
                      Recommended Action
                    </th>
                    <th
                      class="px-2 py-1 border sticky top-0 z-30 bg-primary text-center"
                    >
                      Chosen Action
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(row, index) in paginatedData" :key="index">
                    <td
                      class="px-2 py-1 border text-center sticky left-0 z-30 bg-white min-w-[60px]"
                    >
                      {{ row.LOC_CD }}
                    </td>
                    <td
                      class="px-2 py-1 border text-center whitespace-nowrap sticky z-30 bg-white min-w-[100px]"
                      style="left: var(--store-col-width)"
                    >
                      {{ capitalizeWords(row.SUB_CLSS_NM) }}
                    </td>
                    <td class="px-2 py-1 border text-center">
                      {{ row.Linear_meter?.toFixed(2) }}
                    </td>
                    <td class="px-2 py-1 border text-center">
                      {{ row.d_units?.toFixed(2) }}
                    </td>
                    <td class="px-2 py-1 border text-center">
                      {{ row.CUST_PEN?.toFixed(2) }}%
                    </td>
                    <td class="px-2 py-1 border text-center">
                      {{ Math.round(row.COVER) }}
                    </td>
                    <td class="px-2 py-1 border text-center">
                      {{ row.MARGIN_PERC?.toFixed(2) }}%
                    </td>
                    <td class="px-2 py-1 border text-center">
                      {{ row.Productivity?.toFixed(2) }}
                    </td>
                    <td class="px-2 py-1 border text-center">
                      {{ row.ASP?.toFixed(2) }}
                    </td>
                    <td class="px-2 py-1 border text-center">
                      {{ row.Performance }}
                    </td>
                    <td class="px-2 py-1 border text-center">
                      {{ mapCategory(row.perf_bucket_p) }}
                    </td>
                    <td class="px-2 py-1 border text-center">
                      {{ mapCategory(row.lm_bucket_p) }}
                    </td>
                    <td class="px-2 py-1 border text-center">
                      {{ mapCategory(row.cover_bucket_p) }}
                    </td>
                    <td class="px-2 py-1 border text-center relative">
                      <div class="inline-block group relative">
                        <span class="font-semibold cursor-pointer">
                          {{ row.perf_bucket_p }}{{ row.lm_bucket_p
                          }}{{ row.cover_bucket_p }}
                        </span>

                        <!-- Tooltip: position based on row index -->
                        <div
                          :class="[
                            'absolute left-1/2 transform -translate-x-1/2 w-56 rounded-md bg-gray-800 text-tx-primary text-xs p-2 shadow-lg z-50 transition-opacity duration-200 pointer-events-none group-hover:pointer-events-auto',
                            index === 0 ? 'top-full mt-2' : 'bottom-full mb-2',
                            'opacity-0 group-hover:opacity-100',
                          ]"
                        >
                          <div>
                            <strong>Performance Grade:</strong>
                            {{ mapCategory(row.perf_bucket_p) }}
                          </div>
                          <div>
                            <strong>LM Grade:</strong>
                            {{ mapCategory(row.lm_bucket_p) }}
                          </div>
                          <div>
                            <strong>Cover Grade:</strong>
                            {{ mapCategory(row.cover_bucket_p) }}
                          </div>
                        </div>
                      </div>
                    </td>

                    <!-- Recommendation column -->
                    <td class="px-2 py-1 border text-center">
                      <span
                        :class="`inline-block capitalize px-2 py-1 rounded-full font-semibold text-xs ${getActionClass(
                          row.action_p
                        )}`"
                      >
                        {{ row.action_p }}
                      </span>
                    </td>
                    <td class="px-2 py-1 border text-center">
                      <select
                        v-model="row.new_action"
                        @change="toggleAction(row, row.new_action)"
                        class="border font-semibold rounded px-2 py-1 text-xs capitalize"
                        :class="
                          actionColor(
                            row.new_action,
                            row.new_action,
                            row.action_p
                          )
                        "
                        :disabled="isDisabled"
                      >
                        <option
                          v-for="option in actionOptions"
                          :key="option"
                          :value="option"
                        >
                          {{ option }}
                        </option>
                      </select>
                    </td>
                  </tr>
                  <!-- ...existing code... -->
                </tbody>
              </table>
              <div
                v-if="isFetchingNextPage"
                class="flex items-center justify-center space-x-2 text-gray-500 py-4"
              >
                <Loader class="animate-spin w-4 h-4 text-secondary" />
              </div>
            </div>
          </div>
          <!-- Pagination Controls moved outside table -->
        </div>
      </main>
    </div>
  </div>
  <transition name="fade">
    <div
      v-if="weightsConfigure"
      class="fixed inset-0 pr-10 flex items-center justify-end bg-black bg-opacity-50 z-50"
    >
      <WeightsCalcu
        @close="weightsConfigure = false" :isDisabled="isDisabled"
        @apply="handleApplyWeights"
      />
    </div>
  </transition>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.fade-enter-to,
.fade-leave-from {
  opacity: 1;
}

.run-button {
  padding: 10px 0;
  background-color: #abc4ff;
  color: #fff;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  border-radius: 6px;
  transition: background-color 0.2s ease, color 0.2s ease;
  width: 80px;
}

.run-button:hover {
  background-color: #15803d;
  /* Tailwind's tertiary */
}

/* Custom loading animation */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
